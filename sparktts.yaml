name: sparktts
channels:
  - conda-forge
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - asttokens=3.0.0=pyhd8ed1ab_1
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - comm=0.2.2=pyhd8ed1ab_1
  - debugpy=1.8.11=py312h6a678d5_0
  - decorator=5.2.1=pyhd8ed1ab_0
  - exceptiongroup=1.2.2=pyhd8ed1ab_1
  - executing=2.1.0=pyhd8ed1ab_1
  - expat=2.6.4=h6a678d5_0
  - importlib-metadata=8.6.1=pyha770c72_0
  - ipykernel=6.29.5=pyh3099207_0
  - ipython=9.0.2=pyhfb0248b_0
  - ipython_pygments_lexers=1.1.1=pyhd8ed1ab_0
  - jedi=0.19.2=pyhd8ed1ab_1
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.7.2=pyh31011fe_1
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libsodium=1.0.18=h36c2ea0_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - openssl=3.0.16=h5eee18b_0
  - packaging=24.2=pyhd8ed1ab_2
  - parso=0.8.4=pyhd8ed1ab_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pip=25.0=py312h06a4308_0
  - platformdirs=4.3.7=pyh29332c3_0
  - prompt-toolkit=3.0.50=pyha770c72_0
  - psutil=5.9.0=py312h5eee18b_1
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pygments=2.19.1=pyhd8ed1ab_0
  - python=3.12.9=h5148396_0
  - python-dateutil=2.9.0.post0=pyhff2d567_1
  - pyzmq=26.2.0=py312h6a678d5_0
  - readline=8.2=h5eee18b_0
  - setuptools=75.8.0=py312h06a4308_0
  - six=1.17.0=pyhd8ed1ab_0
  - sqlite=3.45.3=h5eee18b_0
  - stack_data=0.6.3=pyhd8ed1ab_1
  - tk=8.6.14=h39e8969_0
  - tornado=6.4.2=py312h5eee18b_0
  - traitlets=5.14.3=pyhd8ed1ab_1
  - typing_extensions=4.13.0=pyh29332c3_1
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.45.1=py312h06a4308_0
  - xz=5.6.4=h5eee18b_1
  - zeromq=4.3.5=h6a678d5_0
  - zipp=3.21.0=pyhd8ed1ab_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - aiofiles==23.2.1
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.9.0
      - certifi==2025.1.31
      - cffi==1.17.1
      - charset-normalizer==3.4.1
      - click==8.1.8
      - einops==0.8.1
      - einx==0.3.0
      - fastapi==0.115.12
      - ffmpy==0.5.0
      - filelock==3.18.0
      - frozendict==2.4.6
      - fsspec==2025.3.2
      - gradio==5.18.0
      - gradio-client==1.7.2
      - h11==0.14.0
      - httpcore==1.0.7
      - httpx==0.28.1
      - huggingface-hub==0.30.1
      - idna==3.10
      - jinja2==3.1.6
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - mdurl==0.1.2
      - mpmath==1.3.0
      - networkx==3.4.2
      - numpy==2.2.3
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.4.127
      - nvidia-cuda-nvrtc-cu12==12.4.127
      - nvidia-cuda-runtime-cu12==12.4.127
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==**********
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.4.127
      - omegaconf==2.3.0
      - orjson==3.10.16
      - pandas==2.2.3
      - pillow==11.1.0
      - pycparser==2.22
      - pydantic==2.11.1
      - pydantic-core==2.33.0
      - pydub==0.25.1
      - python-multipart==0.0.20
      - pytz==2025.2
      - pyyaml==6.0.2
      - regex==2024.11.6
      - requests==2.32.3
      - rich==14.0.0
      - ruff==0.11.2
      - safehttpx==0.1.6
      - safetensors==0.5.2
      - semantic-version==2.10.0
      - shellingham==1.5.4
      - sniffio==1.3.1
      - soundfile==0.12.1
      - soxr==0.5.0.post1
      - starlette==0.46.1
      - sympy==1.13.1
      - tokenizers==0.20.3
      - tomlkit==0.13.2
      - torch==2.5.1
      - torchaudio==2.5.1
      - tqdm==4.66.5
      - transformers==4.46.2
      - triton==3.1.0
      - typer==0.15.2
      - typing-inspection==0.4.0
      - tzdata==2025.2
      - urllib3==2.3.0
      - uvicorn==0.34.0
      - websockets==15.0.1
prefix: /root/miniconda3/envs/sparktts
