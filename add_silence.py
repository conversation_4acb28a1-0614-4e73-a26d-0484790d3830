import os
import soundfile as sf
import numpy as np

def add_silence_to_audio(input_path, silence_duration=1.0):
    """
    Adds silence to the beginning of an audio file and returns the new audio and sample rate.
    """
    audio, sr = sf.read(input_path)

    # Handle stereo or mono
    if audio.ndim == 1:  # mono
        silence = np.zeros(int(sr * silence_duration))
    else:  # stereo or multi-channel
        silence = np.zeros((int(sr * silence_duration), audio.shape[1]))

    audio_with_silence = np.concatenate((silence, audio), axis=0)
    return audio_with_silence, sr


def process_audio_files(audio_paths, silence_duration=1.0):
    """
    Processes a list of audio files by adding silence and saving them.

    Args:
        audio_paths (List[str]): List of input audio file paths.
        silence_duration (float): Duration of silence to add in seconds.
    """
    for path in audio_paths:
        output_path = f"{os.path.splitext(path)[0]}_with_silence{os.path.splitext(path)[1]}"
        modified_audio, sr = add_silence_to_audio(path, silence_duration)
        sf.write(output_path, modified_audio, sr)
        print(f"✅ Saved: {output_path} (added {silence_duration:.1f}s silence)")

def main():
    ref_files = [
        "resources/man1.mp3",
        "resources/man2.mp3",
        "resources/man3.mp3",
        "resources/sp1.mp3",
        "resources/sp2.mp3"
    ]
    process_audio_files(ref_files)

if __name__ == "__main__":
    main()
