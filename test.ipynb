{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/workspace/miniconda3/envs/test/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from huggingface_hub import snapshot_download\n", "\n", "# snapshot_download(\"SparkAudio/Spark-TTS-0.5B\", local_dir=\"pretrained_models/Spark-TTS-0.5B\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/workspace/miniconda3/envs/sparktts/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["# import os\n", "# import torch\n", "# import soundfile as sf\n", "# from datetime import datetime\n", "\n", "# from cli.SparkTTS import SparkTTS"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ sp2_16k.mp3\n", "✓ sp1_16k.mp3\n", "✓ man3_16k.mp3\n", "✓ man2_16k.mp3\n", "✓ man1_16k.mp3\n"]}], "source": ["from pathlib import Path\n", "from pydub import AudioSegment   # pip install pydub; needs ffmpeg in PATH\n", "\n", "src_dir = Path(\"resources\")\n", "\n", "for mp3 in src_dir.glob(\"*.mp3\"):\n", "    audio = AudioSegment.from_file(mp3)       # load original\n", "    audio = audio.set_frame_rate(16_000)      # resample\n", "    audio = audio.set_channels(1)             # mono keeps embeddings clean\n", "    out = mp3.with_name(f\"{mp3.stem}_16k.mp3\")\n", "    audio.export(out, format=\"mp3\", bitrate=\"64k\")   # save new file\n", "    print(f\"✓ {out.name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/workspace/miniconda3/envs/sparktts/lib/python3.12/site-packages/torch/nn/utils/weight_norm.py:143: FutureWarning: `torch.nn.utils.weight_norm` is deprecated in favor of `torch.nn.utils.parametrizations.weight_norm`.\n", "  WeightNorm.apply(module, name, dim)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Note: Some known model tensors (like mel_transformer) are missing and will be auto-regenerated.\n", "CUDA Device in use: NVIDIA RTX 4000 Ada Generation\n", "resources/man1_16k.mp3 16000\n", "File man1_16k.wav is saved at experiments_results/other_changes/higher_temp_1_1/man1_16k.wav.\n", "resources/man2_16k.mp3 16000\n", "File man2_16k.wav is saved at experiments_results/other_changes/higher_temp_1_1/man2_16k.wav.\n", "resources/man3_16k.mp3 16000\n", "File man3_16k.wav is saved at experiments_results/other_changes/higher_temp_1_1/man3_16k.wav.\n", "resources/sp1_16k.mp3 16000\n", "File sp1_16k.wav is saved at experiments_results/other_changes/higher_temp_1_1/sp1_16k.wav.\n", "resources/sp2_16k.mp3 16000\n", "File sp2_16k.wav is saved at experiments_results/other_changes/higher_temp_1_1/sp2_16k.wav.\n"]}], "source": ["import os\n", "import torch\n", "import soundfile as sf\n", "from datetime import datetime\n", "\n", "from cli.SparkTTS import SparkTTS\n", "\n", "\n", "model_dir = 'pretrained_models/Spark-TTS-0.5B' \n", "save_dir = 'experiments_results/other_changes/higher_temp_1_1/'\n", "device = 0\n", "\n", "text1 = 'Okay, alright. Understand that <PERSON>, being the police chief, may have raised his voice. Let\\'s get back to the main point'\n", "text2 = \"Why do you keep sending guys like him? I said clearly no, but—\"\n", "text3 = \"The call just blew out. Did this guy do it? He is just going out, live.\"\n", "text4 = \"You know how great airdays is? While studying I sprayed mist to wake up.\"\n", "text5 = \"So thin you forget you are using them, the K-F ninety-four airdays.\"\n", "texts = [text1, text2, text3, text4, text5]\n", "ref_audio_promt = ''\n", "ref_audio_paths = ['resources/man1.mp3', \n", "                   'resources/man2.mp3', \n", "                   'resources/man3.mp3', \n", "                   'resources/sp1.mp3', \n", "                   'resources/sp2.mp3']\n", "ref_audio_paths2 = ['resources/man1_16k.mp3', \n", "                   'resources/man2_16k.mp3', \n", "                   'resources/man3_16k.mp3', \n", "                   'resources/sp1_16k.mp3', \n", "                   'resources/sp2_16k.mp3']\n", "gender = 'male'     # choices = ['male', 'female']\n", "pitch = 'moderate'  # choices=[\"very_low\", \"low\", \"moderate\", \"high\", \"very_high\"]\n", "speed = 'low'       # choices=[\"very_low\", \"low\", \"moderate\", \"high\", \"very_high\"]\n", "\n", "if torch.cuda.is_available():\n", "    # System with CUDA supportss\n", "    device = torch.device(f\"cuda:{device}\")\n", "\n", "# Initialize the model\n", "model = SparkTTS(model_dir, device)\n", "\n", "# Generate unique filename using timestamp\n", "# timestamp = datetime.now().strftime(\"%Y%m%d%H%M%S\")\n", "# save_path = os.path.join(save_dir, f\"{timestamp}.wav\")\n", "\n", "for text, ref_audio_path in zip(texts, ref_audio_paths2):\n", "    print(ref_audio_path, sf.info(ref_audio_path).samplerate)\n", "    name = ref_audio_path.split('/')[-1].split('.')[0]\n", "    save_path = os.path.join(save_dir, f\"{name}.wav\")\n", "    # Perform inference and save the output audio\n", "    with torch.no_grad():\n", "        wav = model.inference(\n", "            text,\n", "            ref_audio_path,\n", "            temperature=0.5,\n", "            # prompt_text=ref_audio_promt,\n", "            # gender=gender,\n", "            # pitch=pitch,\n", "            # speed=speed,\n", "        )\n", "        sf.write(save_path, wav, samplerate=16000)\n", "        print(f'File {name}.wav is saved at {save_path}.')\n", "\n", "# print(f'File {timestamp}.wav is saved.')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sparktts", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}