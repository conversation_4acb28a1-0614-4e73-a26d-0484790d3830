#!/usr/bin/env python3
"""
Test script for word pause functionality in Spark-TTS.
This script demonstrates how to generate audio with pauses between words.
"""

import sys
import os
import logging

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from cli.tts_cli import generate_tts_audio

def test_word_pauses():
    """Test the word pause functionality with different pause durations."""
    
    # Configure logging
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
    
    # Test text
    test_text = "Hello world this is a test"
    
    # Test different pause durations
    pause_durations = [0.2, 0.5, 1.0]
    
    print("🎵 Testing word pause functionality...")
    print(f"📝 Test text: '{test_text}'")
    print()
    
    for pause_duration in pause_durations:
        print(f"⏱️  Testing with {pause_duration}s pauses between words...")
        
        try:
            output_file = generate_tts_audio(
                text=test_text,
                gender="female",  # Using female voice for testing
                word_pause_duration=pause_duration,
                save_dir="test_results"
            )
            print(f"✅ Generated: {output_file}")
            
        except Exception as e:
            print(f"❌ Error with {pause_duration}s pause: {e}")
        
        print()
    
    # Test without pauses for comparison
    print("🔄 Generating reference audio without pauses...")
    try:
        reference_file = generate_tts_audio(
            text=test_text,
            gender="female",
            save_dir="test_results"
        )
        print(f"✅ Reference (no pauses): {reference_file}")
    except Exception as e:
        print(f"❌ Error generating reference: {e}")

def test_with_voice_cloning():
    """Test word pauses with voice cloning if a prompt audio is available."""
    
    # Look for a sample audio file
    sample_audio_paths = [
        "resources/man1.mp3",
        "resources/sp1.mp3", 
        "example/prompt_audio.wav"
    ]
    
    prompt_audio = None
    for path in sample_audio_paths:
        if os.path.exists(path):
            prompt_audio = path
            break
    
    if prompt_audio:
        print(f"🎤 Testing word pauses with voice cloning using: {prompt_audio}")
        
        test_text = "This is voice cloning with word pauses"
        
        try:
            output_file = generate_tts_audio(
                text=test_text,
                prompt_speech_path=prompt_audio,
                word_pause_duration=0.5,
                save_dir="test_results"
            )
            print(f"✅ Voice cloning with pauses: {output_file}")
            
        except Exception as e:
            print(f"❌ Error with voice cloning: {e}")
    else:
        print("⚠️  No sample audio found for voice cloning test")

if __name__ == "__main__":
    print("🚀 Starting Spark-TTS word pause tests...")
    print("=" * 50)
    
    # Create test results directory
    os.makedirs("test_results", exist_ok=True)
    
    # Run tests
    test_word_pauses()
    print()
    test_with_voice_cloning()
    
    print("=" * 50)
    print("✨ Tests completed! Check the 'test_results' directory for generated audio files.")
