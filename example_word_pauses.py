#!/usr/bin/env python3
"""
Example script demonstrating how to use word pauses in Spark-TTS.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from cli.tts_cli import generate_tts_audio

def main():
    """Generate audio with word pauses."""
    
    # Example text
    text = "Welcome to Spark TTS with word pauses"
    
    print("🎵 Generating audio with word pauses...")
    print(f"📝 Text: '{text}'")
    
    # Generate audio with 0.5 second pauses between words
    output_file = generate_tts_audio(
        text=text,
        gender="female",
        word_pause_duration=0.5,  # 0.5 seconds between words
        save_dir="examples"
    )
    
    print(f"✅ Audio generated: {output_file}")
    print("🎧 Play the audio to hear the pauses between words!")

if __name__ == "__main__":
    main()
