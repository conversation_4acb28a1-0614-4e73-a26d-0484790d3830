# Spark TTS API

This is a FastAPI application that provides text-to-speech conversion functionality with S3 storage integration.

## Setup

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Create a `.env` file in the root directory with the following variables:
```env
# AWS Credentials
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1

# S3 Configuration
S3_BUCKET_NAME=your-bucket-name
```

## Running the API

Start the FastAPI server with:
```bash
uvicorn api.main:app --reload
```

The API will be available at `http://localhost:8000`

## API Endpoints

### POST /tts
Convert text to speech and store in S3.

**Parameters:**
- `text` (required): The text to convert to speech
- `prompt_audio` (optional): Audio file for voice cloning
- `gender` (optional): "male" or "female"
- `pitch` (optional): "very_low", "low", "moderate", "high", "very_high"
- `speed` (optional): "very_low", "low", "moderate", "high", "very_high"
- `emotion` (optional): Emotion parameter
- `seed` (optional): Seed for voice generation

**Response:**
```json
{
    "status": "success",
    "audio_url": "https://your-bucket.s3.amazonaws.com/tts/20240321123456_uuid.wav",
    "message": "Text-to-speech conversion completed successfully"
}
```

### GET /health
Health check endpoint.

**Response:**
```json
{
    "status": "healthy"
}
```

## API Documentation

Once the server is running, you can access:
- Interactive API documentation: `http://localhost:8000/docs`
- Alternative API documentation: `http://localhost:8000/redoc` 