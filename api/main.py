import os
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
import uuid
from datetime import datetime
import sys
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from cli.tts_cli import generate_tts_audio

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Spark TTS API",
    description="API for text-to-speech conversion with S3 storage",
    version="1.0.0"
)

# Initialize S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
    aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
    region_name=os.getenv('AWS_REGION', 'us-east-1')
)

BUCKET_NAME = os.getenv('S3_BUCKET_NAME')

def upload_to_s3(file_path: str, object_name: str = None) -> str:
    """Upload a file to S3 bucket"""
    if object_name is None:
        object_name = os.path.basename(file_path)
    
    try:
        s3_client.upload_file(file_path, BUCKET_NAME, object_name)
        url = f"https://{BUCKET_NAME}.s3.amazonaws.com/{object_name}"
        return url
    except ClientError as e:
        logger.error(f"Error uploading to S3: {e}")
        raise HTTPException(status_code=500, detail="Failed to upload file to S3")

@app.post("/tts")
async def text_to_speech(
    text: str,
    prompt_audio: UploadFile = File(None),
    gender: str = None,
    pitch: str = "moderate",
    speed: str = "moderate",
    emotion: str = None,
    seed: int = None
):
    """
    Convert text to speech and store the audio file in S3.
    
    Parameters:
    - text: The text to convert to speech
    - prompt_audio: Optional audio file for voice cloning
    - gender: Optional gender parameter ("male"/"female")
    - pitch: Optional pitch parameter
    - speed: Optional speed parameter
    - emotion: Optional emotion parameter
    - seed: Optional seed for voice generation
    """
    try:
        # Create a temporary directory for processing
        temp_dir = "temp_audio"
        os.makedirs(temp_dir, exist_ok=True)
        
        # Handle prompt audio if provided
        prompt_audio_path = None
        if prompt_audio:
            prompt_audio_path = os.path.join(temp_dir, f"prompt_{uuid.uuid4()}.wav")
            with open(prompt_audio_path, "wb") as f:
                f.write(await prompt_audio.read())
        
        # Generate TTS audio
        output_file = generate_tts_audio(
            text=text,
            gender=gender,
            pitch=pitch,
            speed=speed,
            emotion=emotion,
            seed=seed,
            prompt_speech_path=prompt_audio_path,
            save_dir=temp_dir
        )
        
        # Generate a unique S3 object name
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        s3_object_name = f"tts/{timestamp}_{uuid.uuid4()}.wav"
        
        # Upload to S3
        audio_url = upload_to_s3(output_file, s3_object_name)
        
        # Clean up temporary files
        if prompt_audio_path and os.path.exists(prompt_audio_path):
            os.remove(prompt_audio_path)
        if os.path.exists(output_file):
            os.remove(output_file)
        
        return JSONResponse({
            "status": "success",
            "audio_url": audio_url,
            "message": "Text-to-speech conversion completed successfully"
        })
        
    except Exception as e:
        logger.error(f"Error in text-to-speech conversion: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"} 